import React from "react";
import FileUpload from "./FileUpload";
import { useFileOperations } from "../contexts/FileContext";
import { Gig } from "../types/gig";

interface FileUploadModalProps {
  title?: string;
  successMessage?: string;
  showFileUpload: boolean;
  onClose: () => void;
  onUploadSuccess: (gig: Gig) => void;
}

const FileUploadModal: React.FC<FileUploadModalProps> = ({
  title = "Upload Your File",
  successMessage = "File uploaded successfully!",
  showFileUpload,
  onClose,
  onUploadSuccess,
}) => {
  const { uploadState, resetAll } = useFileOperations();

  if (!showFileUpload) {
    return null;
  }

  const handleClose = () => {
    resetAll();
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">{title}</h2>
            <button
              onClick={handleClose}
              className="text-gray-500 hover:text-gray-700 text-2xl"
              disabled={uploadState.isUploading}
            >
              ×
            </button>
          </div>

          <FileUpload onUploadSuccess={onUploadSuccess} />

          {uploadState.success && (
            <div className="mt-4 text-center">
              <p className="text-green-600 mb-2">{successMessage}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FileUploadModal;
