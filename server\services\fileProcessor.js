import fs from "fs";
import path from "path";
import mammoth from "mammoth";
import { cleanupFile } from "../services/fileUtils.js";
import {} from "pdfjs-dist/legacy/build/pdf.mjs";

let pdfjs;
try {
  pdfjs = await import("pdfjs-dist/legacy/build/pdf.mjs");
} catch (error) {
  console.warn("PDF processing not available:", error.message);
}

/**
 * Extract text from various file types
 * @param {string} filePath - Path to the uploaded file
 * @param {string} originalName - Original filename
 * @returns {Promise<string>} - Extracted text content
 */
export const extractTextFromFile = async (filePath, originalName) => {
  try {
    const extension = path.extname(originalName).toLowerCase();
    let extractedText = "";

    switch (extension) {
      case ".txt":
        extractedText = await extractTextFromTxt(filePath);
        break;
      case ".pdf":
        extractedText = await extractTextFromPdf(filePath);
        break;
      case ".docx":
        extractedText = await extractTextFromDocx(filePath);
        break;
      default:
        throw new Error(`Unsupported file type: ${extension}`);
    }

    // Clean up the uploaded file after processing
    cleanupFile(filePath);

    // Validate that we extracted some text
    if (!extractedText || extractedText.trim().length === 0) {
      throw new Error("No text content could be extracted from the file");
    }

    return extractedText.trim();
  } catch (error) {
    // Ensure file cleanup even if processing fails
    cleanupFile(filePath);
    throw error;
  }
};

/**
 * Extract text from .txt files
 * @param {string} filePath - Path to the text file
 * @returns {Promise<string>} - File content
 */
const extractTextFromTxt = async (filePath) => {
  try {
    const content = fs.readFileSync(filePath, "utf8");
    return content;
  } catch (error) {
    throw new Error(`Failed to read text file: ${error.message}`);
  }
};

/**
 * Extract text from PDF files using pdfjs-dist
 * @param {string} filePath - Path to the PDF file
 * @returns {Promise<string>} - Extracted text
 */
const extractTextFromPdf = async (filePath) => {
  if (!pdfjs) {
    throw new Error(
      "PDF processing is not available. Please try a different file format."
    );
  }

  try {
    const dataBuffer = fs.readFileSync(filePath);
    const uint8Array = new Uint8Array(dataBuffer);

    // Load the PDF document
    const loadingTask = pdfjs.getDocument({
      data: uint8Array,
      verbosity: 0, // Suppress console output
    });

    const pdf = await loadingTask.promise;
    let fullText = "";

    // Extract text from each page
    for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
      const page = await pdf.getPage(pageNum);
      const textContent = await page.getTextContent();

      // Combine text items from the page
      const pageText = textContent.items.map((item) => item.str).join(" ");

      fullText += pageText + "\n";
    }

    if (!fullText || fullText.trim().length === 0) {
      throw new Error(
        "PDF appears to be empty or contains no extractable text"
      );
    }

    return fullText.trim();
  } catch (error) {
    if (error.message.includes("PDF appears to be empty")) {
      throw error;
    }
    throw new Error(`Failed to extract text from PDF: ${error.message}`);
  }
};

/**
 * Extract text from DOC/DOCX files
 * @param {string} filePath - Path to the Word document
 * @returns {Promise<string>} - Extracted text
 */
const extractTextFromDocx = async (filePath) => {
  try {
    const result = await mammoth.extractRawText({ path: filePath });
    if (!result.value || result.value.trim().length === 0) {
      throw new Error(
        "Document appears to be empty or contains no extractable text"
      );
    }

    // Log any warnings from mammoth
    if (result.messages && result.messages.length > 0) {
      console.warn("Document processing warnings:", result.messages);
    }

    return result.value;
  } catch (error) {
    if (error.message.includes("Document appears to be empty")) {
      throw error;
    }
    throw new Error(`Failed to extract text from document: ${error.message}`);
  }
};

/**
 * Validate and sanitize extracted text
 * @param {string} text - Raw extracted text
 * @returns {string} - Cleaned text
 */
export const sanitizeExtractedText = (text) => {
  if (!text || typeof text !== "string") {
    return "";
  }

  // Remove excessive whitespace and normalize line breaks
  let cleaned = text
    .replace(/\r\n/g, "\n") // Normalize line breaks
    .replace(/\r/g, "\n") // Convert remaining \r to \n
    .replace(/\n{3,}/g, "\n\n") // Limit consecutive line breaks
    .replace(/[ \t]{2,}/g, " ") // Replace multiple spaces/tabs with single space
    .trim();

  return cleaned;
};

/**
 * Get file information for logging/debugging
 * @param {string} filePath - Path to the file
 * @param {string} originalName - Original filename
 * @returns {Object} - File information
 */
export const getFileInfo = (filePath, originalName) => {
  try {
    const stats = fs.statSync(filePath);
    return {
      originalName,
      size: stats.size,
      extension: path.extname(originalName).toLowerCase(),
      uploadTime: new Date().toISOString(),
    };
  } catch (error) {
    return {
      originalName,
      error: error.message,
    };
  }
};
