import multer from "multer";
import path from "path";
import fs from "fs";

// Configuration constants
const UPLOADS_DIR = "uploads";

/**
 * Initialize storage directory
 * Creates the uploads directory if it doesn't exist
 */
export const initializeStorage = () => {
  if (!fs.existsSync(UPLOADS_DIR)) {
    fs.mkdirSync(UPLOADS_DIR, { recursive: true });
  }
};

/**
 * Get multer storage configuration
 * @returns {multer.StorageEngine} Configured multer storage
 */
export const getStorageConfig = () => {
  // Ensure storage directory exists
  initializeStorage();
  
  return multer.diskStorage({
    destination: (req, file, cb) => {
      cb(null, UPLOADS_DIR);
    },
    filename: (req, file, cb) => {
      // Generate unique filename with timestamp
      const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
      const extension = path.extname(file.originalname);
      cb(null, file.fieldname + "-" + uniqueSuffix + extension);
    },
  });
};

/**
 * Get uploads directory path
 * @returns {string} The uploads directory path
 */
export const getUploadsDir = () => UPLOADS_DIR;
