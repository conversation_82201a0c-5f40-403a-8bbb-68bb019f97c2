import fs from "fs";

/**
 * Cleanup function to remove uploaded files
 * @param {string} filePath - Path to the file to be removed
 */
export const cleanupFile = (filePath) => {
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
  } catch (error) {
    console.error("Error cleaning up file:", error);
  }
};

/**
 * Check if file exists
 * @param {string} filePath - Path to the file to check
 * @returns {boolean} True if file exists, false otherwise
 */
export const fileExists = (filePath) => {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    console.error("Error checking file existence:", error);
    return false;
  }
};

/**
 * Get file stats
 * @param {string} filePath - Path to the file
 * @returns {fs.Stats|null} File stats or null if error
 */
export const getFileStats = (filePath) => {
  try {
    return fs.statSync(filePath);
  } catch (error) {
    console.error("Error getting file stats:", error);
    return null;
  }
};
